// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const PayModel = sequelize.define(
// 	"pay",
// 	{
// 		name: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		amount: {
// 			type: DataTypes.DOUBLE,
// 			allowNull: false,
// 		},
// 		date: {
// 			type: DataTypes.DATE,
// 			allowNull: false,
// 		},
// 		has_portfolio: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		supplier: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		title: {
// 			type: DataTypes.TEXT,
// 			allowNull: false,
// 		},
// 		type: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		user_id: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		state: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 	},
// 	{ timestamps: false }
// );
// module.exports = PayModel;
const { DataTypes } = require("sequelize");

function definePayModel(sequelize) {
	return sequelize.define(
		"pay",
		{
			name: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			amount: {
				type: DataTypes.DOUBLE,
				allowNull: false,
			},
			date: {
				type: DataTypes.DATE,
				allowNull: false,
			},
			title: {
				type: DataTypes.TEXT,
				allowNull: false,
			},
			tax: {
				type: DataTypes.DOUBLE,
				allowNull: false,
			},
			net_amount: {
				type: DataTypes.DOUBLE,
				allowNull: false,
			},
			is_closed: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			is_list: {
				type: DataTypes.STRING,
				allowNull: false,
			},
		},
		{
			timestamps: false,

			underscored: true,
		}
	);
}

module.exports = definePayModel;
