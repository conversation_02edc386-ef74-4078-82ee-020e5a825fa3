const express = require("express");
const receivesController = require("./../controllers/receivesController");
const router = express.Router();
const authController = require("../controllers/authController");

router
	.route("/")
	.get(receivesController.getAllReceives)
	.post(authController.restrictTo("addReceive"), receivesController.addReceive)
	.delete(
		authController.restrictTo("deleteReceive"),
		receivesController.deleteReceives
	);
router.route("/:id").get(receivesController.getReceiveById);

module.exports = router;
