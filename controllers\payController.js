const catchAsync = require("../utils/catchAsync");
const AppError = require("../utils/appError");
const { Op } = require("sequelize");
const { getModel } = require("../utils/modelSelect");

exports.addPay = catchAsync(async (req, res, next) => {
	const PayModel = getModel(req.headers["x-year"], "pay");
	const DueModel = getModel(req.headers["x-year"], "due");

	try {
		await req.db.transaction(async (t) => {
			let total = 0;
			let duesIds = [];
			const date = new Date();
			const timezoneOffset = date.getTimezoneOffset();
			const adjustedDate = new Date(date.getTime() - timezoneOffset * 60000);
			const paidAt = adjustedDate.toISOString().slice(0, 19).replace("T", " ");
			req.body.selection.forEach((el) => {
				total = total + el.amount;
				duesIds.push(el.id);
			});
			const titles = req.body.selection.map((el) => el.title);
			const uniqueTitles = [...new Set(titles)];
			const joinedUniqueTitles = uniqueTitles.join(" + ");
			console.log(`req.body`, req.body.name);
			const pay = await PayModel.create(
				{
					name: req.body.name,
					amount: req.body.amount,
					date: paidAt,
					title: joinedUniqueTitles,
					tax: req.body.tax,
					is_closed: 0,
					is_list: req.body.is_list,
					net_amount: req.body.net_amount,
				},
				{ transaction: t }
			);
			await DueModel.update(
				{
					paid_at: paidAt,
					pay_id: pay.id,
				},
				{
					where: { id: { [Op.in]: duesIds } },
					transaction: t,
				}
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		res.status(500).json({
			state: "error",
			error: error.message,
		});
	}
});

exports.getAllPays = catchAsync(async (req, res, next) => {
	const PayModel = getModel(req.headers["x-year"], "pay");
	const page = Number(req.query.page) || 0;
	const limit = Number(req.query.limit) || 0;
	let pays = [];
	if (page === 0 && limit === 0) {
		pays = await PayModel.findAll({
			order: [["id", "DESC"]],
			raw: true,
		});
	} else {
		pays = await PayModel.findAll({
			order: [["id", "DESC"]],
			limit: limit,
			offset: limit * page,
			raw: true,
		});
	}

	const total = await PayModel.count();
	res.status(200).json({
		state: "success",
		pays,
		total,
	});
});
exports.getPay = catchAsync(async (req, res, next) => {
	const PayModel = getModel(req.headers["x-year"], "pay");
	const DueModel = getModel(req.headers["x-year"], "due");
	const ClauseModel = getModel(req.headers["x-year"], "clause");
	const DuesListModel = getModel(req.headers["x-year"], "duesList");

	const pay = await PayModel.findByPk(req.params.id, {
		raw: true,
		include: [
			{
				model: ClauseModel,
				as: "clause",
			},
		],
	});
	if (!pay) {
		return next(new AppError(" سند الصرف غير موجود", 404));
	}
	const dues = await DueModel.findAll({
		where: { pay_id: req.params.id },
		include: [
			{
				model: DuesListModel,
				attributes: ["id", "title"],
			},
		],
	});
	pay.dues = dues;
	res.status(200).json({
		status: "success",
		pay,
	});
});
exports.deletePay = catchAsync(async (req, res, next) => {
	const PayModel = getModel(req.headers["x-year"], "pay");
	const DueModel = getModel(req.headers["x-year"], "due");

	try {
		await req.db.transaction(async (t) => {
			const pay = await PayModel.findByPk(req.params.id, { transaction: t });
			if (pay.is_closed) {
				return next(new AppError("لا يمكن حذف سند صرف مغلق", 404));
			}
			await PayModel.destroy({
				where: { id: req.params.id },
				transaction: t,
			});
			await DueModel.update(
				{
					state: "قابل للدفع",
					paid_at: null,
					pay_id: null,
				},
				{
					where: { pay_id: req.params.id },
					transaction: t,
				}
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error.message, 500));
	}
});
