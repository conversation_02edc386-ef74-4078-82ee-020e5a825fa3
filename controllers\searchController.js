const catchAsync = require("../utils/catchAsync");
const { Op } = require("sequelize");
const { getModel } = require("../utils/modelSelect");

exports.search = catchAsync(async (req, res, next) => {
	const DueModel = getModel(req.headers["x-year"], "due");
	const BeneficiaryModel = getModel(req.headers["x-year"], "beneficiary");
	const DueListModel = getModel(req.headers["x-year"], "duesList");
	const DepartmentModel = getModel(req.headers["x-year"], "department");
	let results = [];

	console.log(`req.query.searchBy`, req.query.searchBy);
	if (req.query.searchBy === "name") {
		results = await DueModel.findAll({
			where: {
				pay_id: null,
			},
			include: [
				{
					model: DueListModel,
					attributes: ["id", "type", "date"],
					include: [
						{
							model: DepartmentModel,
							attributes: ["name"],
						},
					],
				},
				{
					model: BeneficiaryModel,
					attributes: ["name"],
					where: {
						name: {
							[Op.like]: `%${req.query.name}%`,
						},
					},
				},
			],
			order: [["id", "DESC"]],
		});
	} else {
		results = await DueModel.findAll({
			where: { dues_list_id: req.query.number },
			include: [
				{
					model: DueListModel,
					attributes: ["id", "type", "date"],
					include: [
						{
							model: DepartmentModel,
							attributes: ["name"],
						},
					],
				},
				{
					model: BeneficiaryModel,
					attributes: ["name"],
					where: {
						name: {
							[Op.like]: `%${req.query.name}%`,
						},
					},
				},
			],
			order: [["id", "DESC"]],
		});
	}
	console.log(`results`, results);
	res.status(200).json({
		state: "success",
		results,
	});
});
