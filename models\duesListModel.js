// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const DuesListModel = sequelize.define(
// 	"dues_list",
// 	{
// 		title: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		// total: {
// 		// 	type: DataTypes.DOUBLE,
// 		// 	allowNull: false,
// 		// },
// 		date: {
// 			type: DataTypes.DATEONLY,
// 			allowNull: false,
// 		},
// 		state: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 			defaultValue: "underReview",
// 		},
// 		receive_id: {
// 			type: DataTypes.INTEGER,
// 		},
// 		message_sent: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 			defaultValue: 0,
// 		},
// 		type: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 	},
// 	{ timestamps: false }
// );
// module.exports = DuesListModel;
const { DataTypes } = require("sequelize");

function defineDuesListModel(sequelize, DepartmentModel) {
	const DuesListModel = sequelize.define(
		"dues_list",
		{
			title: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			// total: {
			//   type: DataTypes.DOUBLE,
			//   allowNull: false,
			// },
			date: {
				type: DataTypes.DATEONLY,
				allowNull: false,
			},
			state: {
				type: DataTypes.STRING,
				allowNull: false,
				defaultValue: "underReview",
			},
			receive_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			message_sent: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: 0,
			},
			type: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			department_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
		},
		{
			timestamps: false,

			underscored: true,
		}
	);
	if (DepartmentModel) {
		DuesListModel.belongsTo(DepartmentModel, { foreignKey: "department_id" });
	}
	return DuesListModel;
}

module.exports = defineDuesListModel;
