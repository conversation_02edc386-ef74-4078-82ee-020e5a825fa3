const catchAsync = require("../utils/catchAsync");
const AppError = require("../utils/appError");

const { Op } = require("sequelize");

const { getModel } = require("../utils/modelSelect");

exports.addReceive = catchAsync(async (req, res, next) => {
	try {
		let receive;
		await req.db.transaction(async (t) => {
			const ReceiveModel = getModel(req.headers["x-year"], "receive");
			const DuesListModel = getModel(req.headers["x-year"], "duesList");
			const DueModel = getModel(req.headers["x-year"], "due");
			receive = await ReceiveModel.create(
				{
					money_from: req.body.from,
					net_amount: req.body.net_amount,
					amount: req.body.amount,
					tax: req.body.tax,
					type: req.body.type,
					check_number: req.body.checkNumber,
					date: req.body.date,
				},
				{ transaction: t, raw: true }
			);

			await DuesListModel.update(
				{ receive_id: receive.id, state: "قابل للدفع" },
				{
					where: { id: req.body.added.map((el) => el.dueList_id) },
					transaction: t,
				}
			);
			await DueModel.update(
				{ state: "قابل للدفع" },
				{
					where: { dues_list_id: req.body.added.map((el) => el.dueList_id) },
					transaction: t,
				}
			);
		});
		res.status(200).json({
			state: "success",
			receive,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
// exports.addMultiReceives = catchAsync(async (req, res, next) => {
// 	const transaction = await req.db.transaction();

// 	try {
// 		const receivesData = req.body.added.map((el) => ({
// 			money_from: req.body.from,
// 			title: el.title,
// 			amount: el.amount,
// 			receiver: "",
// 			type: req.body.type,
// 			check_number: req.body.checkNumber,
// 			dues_list_id: el.duesListId,
// 			date: req.body.date,
// 		}));

// 		const ids = req.body.added.map((el) => el.duesListId);

// 		await ReceiveModel.bulkCreate(receivesData, { transaction });

// 		await DuesListModel.update(
// 			{ money_received: 1, state: "payable" },
// 			{
// 				where: { id: { [Op.in]: ids } },
// 				transaction,
// 			}
// 		);

// 		await DueModel.update(
// 			{ state: "unPaid" },
// 			{
// 				where: { dues_list_id: { [Op.in]: ids } },
// 				transaction,
// 			}
// 		);

// 		await transaction.commit();

// 		res.status(200).json({
// 			state: "success",
// 		});
// 	} catch (error) {
// 		await transaction.rollback();
// 		throw error;
// 	}
// });
exports.getAllReceives = catchAsync(async (req, res, next) => {
	const ReceiveModel = getModel(req.headers["x-year"], "receive");
	const page = Number(req.query.page) || 0;
	const limit = Number(req.query.limit) || 0;
	let receives = [];
	if (page === 0 && limit === 0) {
		receives = await ReceiveModel.findAll({
			order: [["id", "DESC"]],
			raw: true,
		});
	} else {
		receives = await ReceiveModel.findAll({
			order: [["id", "DESC"]],
			limit: limit,
			offset: limit * page,
			raw: true,
		});
	}
	const total = await ReceiveModel.count();
	res.status(200).json({
		state: "success",
		receives,
		total,
	});
});
exports.getReceiveById = catchAsync(async (req, res, next) => {
	const ReceiveModel = getModel(req.headers["x-year"], "receive");
	const DuesListModel = getModel(req.headers["x-year"], "duesList");
	const receive = await ReceiveModel.findByPk(req.params.id, { raw: true });
	let duesLists = [];
	if (receive) {
		duesLists = await DuesListModel.findAll({
			where: {
				receive_id: req.params.id,
			},
			raw: true,
		});
	}
	receive.duesLists = duesLists;
	res.status(200).json({
		state: "success",
		receive,
	});
});
exports.deleteReceives = catchAsync(async (req, res, next) => {
	const ReceiveModel = getModel(req.headers["x-year"], "receive");
	const DuesListModel = getModel(req.headers["x-year"], "duesList");
	const DueModel = getModel(req.headers["x-year"], "due");
	try {
		await req.db.transaction(async (t) => {
			const duesLists = await DuesListModel.findAll({
				where: {
					receive_id: {
						[Op.in]: req.body.ids,
					},
				},
				transaction: t,
				raw: true,
			});
			const dues = await DueModel.findAll({
				where: {
					dues_list_id: {
						[Op.in]: duesLists.map((el) => el.id),
					},
					pay_id: {
						[Op.ne]: null,
					},
				},
				transaction: t,
			});
			if (dues.length > 0) {
				return next(
					new AppError("لا يمكن حذف السند بسبب صرف المستحقات المرتبطة به", 500)
				);
			}
			await DuesListModel.update(
				{ state: "تمت المراجعة" },
				{
					where: { id: { [Op.in]: duesLists.map((el) => el.id) } },
					transaction: t,
				}
			);
			await DueModel.update(
				{ state: "تمت المراجعة" },
				{
					where: { dues_list_id: { [Op.in]: duesLists.map((el) => el.id) } },
					transaction: t,
				}
			);
			await ReceiveModel.destroy({
				where: {
					id: {
						[Op.in]: req.body.ids,
					},
				},
				transaction: t,
			});
		});

		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error.message, 500));
	}
});
