// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const BeneficiaryModel = require("./beneficiaryModel");
// const DueModel = sequelize.define(
// 	"food_due",
// 	{
// 		beneficiary_id: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		month: {
// 			type: DataTypes.INTEGER,
// 			allowNull: true,
// 		},
// 		feeding_days: {
// 			type: DataTypes.INTEGER,
// 			allowNull: true,
// 		},
// 		year: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		dues_list_id: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		amount: {
// 			type: DataTypes.DOUBLE,
// 			allowNull: false,
// 		},
// 		net_amount: {
// 			type: DataTypes.DOUBLE,
// 			allowNull: false,
// 		},
// 		tax: {
// 			type: DataTypes.DOUBLE,
// 			allowNull: false,
// 		},
// 		state: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		added_at: {
// 			type: DataTypes.DATEONLY,
// 			allowNull: false,
// 		},
// 		paid_at: {
// 			type: DataTypes.DATE,
// 			allowNull: false,
// 		},
// 		message_sent: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		pay_id: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		user_id: {
// 			type: DataTypes.INTEGER,
// 			allowNull: false,
// 		},
// 		type: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		shift_bonus_days: {
// 			type: DataTypes.INTEGER,
// 			allowNull: true,
// 		},
// 		extra_hours: {
// 			type: DataTypes.INTEGER,
// 			allowNull: true,
// 		},
// 	},
// 	{ timestamps: false }
// );
// DueModel.belongsTo(BeneficiaryModel, { foreignKey: "beneficiary_id" });
// module.exports = DueModel;
const { DataTypes } = require("sequelize");

function defineDueModel(sequelize, BeneficiaryModel, DuesListModel) {
	const DueModel = sequelize.define(
		"due",
		{
			beneficiary_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			month: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			feeding_days: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			dues_list_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			amount: {
				type: DataTypes.DOUBLE,
				allowNull: false,
			},
			net_amount: {
				type: DataTypes.DOUBLE,
				allowNull: false,
			},
			tax: {
				type: DataTypes.DOUBLE,
				allowNull: false,
			},
			added_at: {
				type: DataTypes.DATEONLY,
				allowNull: false,
			},
			paid_at: {
				type: DataTypes.DATE,
				allowNull: false,
			},
			pay_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			type: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			shift_bonus_days: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			extra_hours: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			travel_allowance_days_100: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			travel_allowance_days_50: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			travel_amount_100: {
				type: DataTypes.DOUBLE,
				allowNull: true,
			},
			travel_amount_50: {
				type: DataTypes.DOUBLE,
				allowNull: true,
			},
			housing_amount: {
				type: DataTypes.DOUBLE,
				allowNull: true,
			},
			petrol_allowance_amount: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
		},
		{
			timestamps: false,
			underscored: true,
		}
	);
	if (BeneficiaryModel) {
		DueModel.belongsTo(BeneficiaryModel, { foreignKey: "beneficiary_id" });
	}
	if (DuesListModel) {
		DueModel.belongsTo(DuesListModel, { foreignKey: "dues_list_id" });
	}
	return DueModel;
}

module.exports = defineDueModel;
