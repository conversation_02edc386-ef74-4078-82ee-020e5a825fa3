const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const { fn, col, literal, Op } = require("sequelize");
const { getModel } = require("../utils/modelSelect");
const { raw } = require("mysql");

exports.getAllDuesLists = catchAsync(async (req, res, next) => {
	try {
		const DuesListModel = getModel(req.headers["x-year"], "duesList");
		const DueModel = getModel(req.headers["x-year"], "due");
		const whereConditions = {};
		if (req.query.state) whereConditions.state = req.query.state;
		const page = Number(req.query.page) || 0;
		const limit = Number(req.query.limit) || 0;
		let duesLists;
		if (page === 0 && limit === 0) {
			console.log(`testtttttttttt88888888ttttt`);
			console.log(`whereConditions`, whereConditions);
			duesLists = await DuesListModel.findAll({
				where: whereConditions,
				order: [["id", "DESC"]],
				raw: true,
			});
		} else {
			duesLists = await DuesListModel.findAll({
				where: whereConditions,
				order: [["id", "DESC"]],
				limit: limit,
				offset: limit * page,
				raw: true,
			});
		}
		const total = await DuesListModel.count({ where: whereConditions });
		const dues = await DueModel.findAll({
			where: {
				dues_list_id: {
					[Op.in]: duesLists.map((el) => el.id),
				},
			},
			attributes: [
				"dues_list_id",
				[fn("SUM", col("amount")), "totalAmount"],
				[fn("SUM", col("tax")), "totalTax"],
				[literal("SUM(amount) - SUM(tax)"), "netAmount"],
			],
			group: ["dues_list_id"],
			raw: true,
		});
		const duesStates = await DueModel.findAll({
			where: {
				dues_list_id: {
					[Op.in]: duesLists.map((el) => el.id),
				},
			},
			attributes: ["id", "dues_list_id", "pay_id"],
			raw: true,
		});

		duesLists.forEach((el) => {
			const due = dues.find((due) => due.dues_list_id === el.id);
			const unPaid = duesStates.filter(
				(dueState) => !dueState.pay_id && dueState.dues_list_id === el.id
			);

			el.amount = due?.totalAmount || 0;
			el.tax = due?.totalTax || 0;
			el.net_amount = due?.netAmount || 0;
			el.is_paid = unPaid.length > 0 ? false : true;
		});
		res.status(200).json({
			state: "success",
			duesLists,
			total,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.getDuesList = catchAsync(async (req, res, next) => {
	const DuesListModel = getModel(req.headers["x-year"], "duesList");
	const DueModel = getModel(req.headers["x-year"], "due");
	const JobTitleModel = getModel(req.headers["x-year"], "jobTitle");
	const JobTypeModel = getModel(req.headers["x-year"], "jobType");
	const JobDegreeModel = getModel(req.headers["x-year"], "jobDegree");
	const BeneficiaryModel = getModel(req.headers["x-year"], "beneficiary");
	const duesList = await DuesListModel.findByPk(req.params.id, {
		raw: true,
	});
	if (!duesList) {
		return next(new AppError("كشف المستحقات غير موجود", 404));
	}
	const dues = await DueModel.findAll({
		where: { dues_list_id: req.params.id },
		include: [
			{
				model: BeneficiaryModel,
				attributes: ["name"],
				include: [
					{
						model: JobTitleModel,
					},
					{
						model: JobTypeModel,
					},
					{
						model: JobDegreeModel,
					},
				],
			},
		],
	});
	duesList.dues = dues;
	res.status(200).json({
		status: "success",
		duesList,
	});
});
exports.addDuesList = catchAsync(async (req, res, next) => {
	try {
		await req.db.transaction(async (t) => {
			const DuesListModel = getModel(req.headers["x-year"], "duesList");
			const DueModel = getModel(req.headers["x-year"], "due");
			const duesList = await DuesListModel.create(
				{
					title: req.body.title,
					date: req.body.date,
					state: "تحت المراجعة",
					type: req.body.type,
					receive_id: null,
					department_id: req.body.department,
				},
				{ transaction: t }
			);
			let dues = [];
			if (req.body.type === "غذاءات") {
				dues = req.body.addedEmployees.map((el) => ({
					...el,
					id: null,
					state: "تحت المراجعة",
					dues_list_id: duesList.id,
					added_at: req.body.date,
					month: req.body.month,
					type: req.body.type,
				}));
			} else if (req.body.type === "اضافي") {
				dues = req.body.addedEmployees.map((el) => ({
					...el,
					id: null,
					state: "تحت المراجعة",
					dues_list_id: duesList.id,
					added_at: req.body.date,
					month: req.body.month,
					type: req.body.type,
				}));
			} else if (req.body.type === "علاوة نوبة") {
				dues = req.body.addedEmployees.map((el) => ({
					...el,
					id: null,
					state: "تحت المراجعة",
					dues_list_id: duesList.id,
					added_at: req.body.date,
					month: req.body.month,
					type: req.body.type,
				}));
			} else if (req.body.type === "بدل سفر") {
				dues = req.body.addedEmployees.map((el) => ({
					...el,
					id: null,
					state: "تحت المراجعة",
					dues_list_id: duesList.id,
					added_at: req.body.date,
					month: req.body.month,
					type: req.body.type,
				}));
			} else if (req.body.type === "مخصصات بترول") {
				const checkDues = await DueModel.findOne({
					where: { month: req.body.month, type: "مخصصات بترول" },
				});

				if (checkDues) {
					throw new AppError("تم صرف مخصصات البترول لهذا الشهر من قبل", 500);
				}
				dues = req.body.addedEmployees.map((el) => ({
					...el,
					id: null,
					state: "تحت المراجعة",
					dues_list_id: duesList.id,
					added_at: req.body.date,
					month: req.body.month,
					type: req.body.type,
				}));
			} else {
				dues = req.body.addedEmployees.map((el) => ({
					...el,
					id: null,
					state: "تحت المراجعة",
					dues_list_id: duesList.id,
					added_at: req.body.date,
					type: req.body.type,
				}));
			}

			await DueModel.bulkCreate(dues, { transaction: t });
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

exports.updateDuesListState = catchAsync(async (req, res, next) => {
	try {
		await req.db.transaction(async (t) => {
			const DuesListModel = getModel(req.headers["x-year"], "duesList");
			const DueModel = getModel(req.headers["x-year"], "due");
			const PayModel = getModel(req.headers["x-year"], "pay");

			if (
				req.body.state === "تحت المراجعة" ||
				req.body.state === "تمت المراجعة"
			) {
				await DuesListModel.update(
					{ state: req.body.state },
					{
						where: {
							id: {
								[Op.in]: req.body.duesLists,
							},
						},
						transaction: t,
					}
				);
				await DueModel.update(
					{ state: req.body.state },
					{
						where: {
							dues_list_id: {
								[Op.in]: req.body.duesLists,
							},
						},
						transaction: t,
					}
				);
			} else if (req.body.state === "قابل للدفع") {
				const duesLists = await DuesListModel.findAll({
					where: {
						id: {
							[Op.in]: req.body.duesLists,
						},
						state: { [Op.notIn]: ["مستلمة", "قابل للدفع"] },
					},
					transaction: t,
				});
				if (duesLists.length > 0) {
					throw new AppError(
						"لا يمكن تغيير الحالة لوجود كشوفات لم يتم استلام المبالغ الخاصة بها",
						500
					);
				}
				await DuesListModel.update(
					{ state: req.body.state },
					{
						where: {
							id: {
								[Op.in]: req.body.duesLists,
							},
						},
						transaction: t,
					}
				);
				await DueModel.update(
					{ state: req.body.state },
					{
						where: {
							dues_list_id: {
								[Op.in]: req.body.duesLists,
							},
						},
						transaction: t,
					}
				);
			} else if (req.body.state === "مغلق") {
				const dues = await DueModel.findAll({
					where: {
						dues_list_id: {
							[Op.in]: req.body.duesLists,
						},
					},
					raw: true,
					transaction: t,
				});
				const unPaid = dues.filter((el) => !el.pay_id);
				if (unPaid.length > 0) {
					return next(
						new AppError(
							"لا يمكن اغلاق هذا الكشف لعدم دفع كافة المستحقات المرتبطه به",
							500
						)
					);
				} else {
					const paysIds = dues.map((el) => el.pay_id);
					await DuesListModel.update(
						{ state: req.body.state },
						{
							where: {
								id: {
									[Op.in]: req.body.duesLists,
								},
							},
							transaction: t,
						}
					);
					await DueModel.update(
						{ state: req.body.state },
						{
							where: {
								dues_list_id: {
									[Op.in]: req.body.duesLists,
								},
							},
							transaction: t,
						}
					);
					await PayModel.update(
						{ is_closed: 1 },
						{
							where: {
								id: {
									[Op.in]: paysIds,
								},
							},
							transaction: t,
						}
					);
				}
			}
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		// Handle AppError instances properly
		if (error instanceof AppError) {
			return next(error);
		}
		// Handle other errors
		return next(
			new AppError(error.message || "حدث خطأ أثناء تحديث حالة الكشف", 500)
		);
	}
});
exports.approveDuesList = catchAsync(async (req, res, next) => {
	const transaction = await req.db.transaction();

	try {
		const DuesListModel = getModel(req.headers["x-year"], "duesList");
		const DueModel = getModel(req.headers["x-year"], "due");

		await DuesListModel.update(
			{ is_approved: req.body.approve },
			{
				where: { id: { [Op.in]: req.body.ids } },
				transaction,
			}
		);

		await DueModel.update(
			{ is_approved: req.body.approve },
			{
				where: { dues_list_id: { [Op.in]: req.body.ids } },
				transaction,
			}
		);

		await transaction.commit();
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		await transaction.rollback();
		return next(new AppError(error, 500));
	}
});
exports.updateDuesList = catchAsync(async (req, res, next) => {
	try {
		await req.db.transaction(async (t) => {
			const DuesListModel = getModel(req.headers["x-year"], "duesList");
			const DueModel = getModel(req.headers["x-year"], "due");
			// check if month and type is petrol allowance and there is another dues list with the same month and type
			if (req.body.type === "مخصصات بترول") {
				await DueModel.destroy({
					where: {
						id: { [Op.in]: req.body.addedEmployees.map((el) => el.db_id) },
					},
					transaction: t,
				});
				const dues = await DueModel.findAll({
					where: {
						month: req.body.month,
						type: "مخصصات بترول",
					},
					transaction: t,
				});
				req.body.addedEmployees.forEach((el) => {
					el.db_id = null;
				});
				if (dues.length > 0) {
					throw new AppError("تم صرف مخصصات البترول لهذا الشهر من قبل", 500);
				}
			}

			await DuesListModel.update(
				{
					title: req.body.title,
					date: req.body.date,
					state: "تحت المراجعة",
					type: req.body.detailedType,
					receive_id: null,
					department_id: req.body.department,
				},
				{
					where: { id: req.params.id },
					transaction: t,
				}
			);

			//updating dues
			let duesToAdd = [];
			let duesToEdit = [];
			let duesToDelete = [];
			req.body.addedEmployees.forEach((el) => {
				if (el.db_id) {
					duesToEdit.push({
						...el,
						id: el.db_id,
						added_at: req.body.date,
						month: req.body.month,
					});
				}
				if (!el.db_id) {
					duesToAdd.push({
						...el,
						id: null,
						state: "تحت المراجعة",
						dues_list_id: req.params.id,
						added_at: req.body.date,
						month: req.body.month,
						type: req.body.type,
					});
				}
			});

			const dbDues = await DueModel.findAll({
				where: {
					dues_list_id: req.params.id,
				},
				raw: true,
			});
			duesToDelete = dbDues.filter(
				(dbDue) =>
					!req.body.addedEmployees
						.filter((el) => el.db_id)
						.some((reqDue) => reqDue.db_id === dbDue.id)
			);
			const duesToDeleteIds = duesToDelete.map((el) => el.id);
			const updateDuesPromises = duesToEdit.map((el) => {
				return DueModel.update(el, {
					where: { id: el.db_id },
					transaction: t,
				});
			});

			await Promise.all(updateDuesPromises);
			await DueModel.destroy({
				where: {
					id: {
						[Op.in]: duesToDeleteIds,
					},
				},
				transaction: t,
			});
			await DueModel.bulkCreate(duesToAdd, {
				transaction: t,
			});
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

exports.deleteDueList = catchAsync(async (req, res, next) => {
	const DuesListModel = getModel(req.headers["x-year"], "duesList");
	const ids = req.query.ids.split(",");
	await DuesListModel.destroy({
		where: { id: { [Op.in]: ids } },
	});

	res.status(200).json({
		state: "success",
	});
});
