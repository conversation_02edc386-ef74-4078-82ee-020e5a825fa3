// modelSelector.js

const { sequelizeElec, sequelizeElec2024 } = require("../connection");
const defineDuesListModel = require("./../models/duesListModel");
const definePayModel = require("./../models/payModel ");
const defineUserModel = require("./../models/userModel");
const definePermissionModel = require("./../models/permissionModel");
const defineJobTitleModel = require("./../models/jobTitleModel");
const defineJobTypeModel = require("./../models/jobTypeModel");
const defineJobDegreeModel = require("./../models/jobDegreeModel");
const defineBeneficiaryModel = require("./../models/beneficiaryModel");
const defineDueModel = require("./../models/dueModel");
const defineDueTypeModel = require("./../models/dueTypesModel");
const defineReceiveModel = require("./../models/receiveModel");
const defineDepartmentModel = require("../models/departmentModel");

function initModels(sequelize) {
	const jobTitle = defineJobTitleModel(sequelize);
	const jobType = defineJobTypeModel(sequelize);
	const jobDegree = defineJobDegreeModel(sequelize);

	const beneficiary = defineBeneficiaryModel(sequelize, {
		JobTitleModel: jobTitle,
		JobTypeModel: jobType,
		JobDegreeModel: jobDegree,
	});
	const department = defineDepartmentModel(sequelize);
	const duesList = defineDuesListModel(sequelize, department);
	const due = defineDueModel(sequelize, beneficiary, duesList);

	return {
		duesList,
		pay: definePayModel(sequelize),
		user: defineUserModel(sequelize),
		permission: definePermissionModel(sequelize),
		jobTitle,
		jobType,
		jobDegree,
		beneficiary,
		due,
		dueType: defineDueTypeModel(sequelize),
		receive: defineReceiveModel(sequelize),
		department,
	};
}

const models = {
	elec: initModels(sequelizeElec),
	"elec-2024": initModels(sequelizeElec2024),
};

// /**
//  * Gets a Sequelize model instance based on year and modelName
//  * @param {string} year - e.g., "2024"
//  * @param {string} modelName - e.g., "duesList"
//  * @returns Sequelize Model instance
//  */
function getModel(year, modelName) {
	const dbKey = year ? `elec-${year}` : "elec";

	if (!models[dbKey]) {
		throw new Error(`Database instance for year '${year}' not configured`);
	}
	if (!models[dbKey][modelName]) {
		throw new Error(`Model '${modelName}' not found for database '${dbKey}'`);
	}

	return models[dbKey][modelName];
}

module.exports = {
	getModel,
};
